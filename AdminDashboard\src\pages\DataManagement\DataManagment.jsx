import React, { useState, useEffect } from 'react'
import { Search, Upload, Edit, Trash2, Plus, X } from 'lucide-react'
import axiosInstance from '../../utils/axiosInstance'
import { API_PATHS } from '../../utils/apiPaths'
import Toast from '../../components/Toast'

const DataManagement = () => {
  const [entries, setEntries] = useState([])
  const [loading, setLoading] = useState(false)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const [toast, setToast] = useState({ visible: false, message: '', type: 'success' })
  const [isEditMode, setIsEditMode] = useState(false)
  const [editingEntryId, setEditingEntryId] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showForm, setShowForm] = useState(false)

  const [formData, setFormData] = useState({
    managementSystem: 'District',
    district: '',
    totalChildren: '',
    outOfSchoolChildren: '',
    gender: { girls: '', boys: '' },
    programType: '',
    date: '',
    poverty: '',
    distance: '',
    other: ''
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleGenderChange = (type, value) => {
    setFormData(prev => ({
      ...prev,
      gender: {
        ...prev.gender,
        [type]: value
      }
    }))
  }

  // API Functions
  const fetchAllEntries = async () => {
    setLoading(true)
    try {
      console.log('🔄 Fetching entries from:', API_PATHS.ENTRIES.GET_ALL_ENTRIES)
      const response = await axiosInstance.get(API_PATHS.ENTRIES.GET_ALL_ENTRIES)

      console.log('📥 Raw API Response:', response)
      console.log('📊 Response Data:', response.data)
      console.log('📊 Response Status:', response.status)

      // Check different possible data structures
      console.log('🔍 Checking data structure:')
      console.log('  - response.data:', response.data)
      console.log('  - response.data.entries:', response.data?.entries)
      console.log('  - response.data.data:', response.data?.data)
      console.log('  - Array.isArray(response.data):', Array.isArray(response.data))

      // Try different extraction methods
      let entriesData = []
      if (response.data?.entries && Array.isArray(response.data.entries)) {
        entriesData = response.data.entries
        console.log('✅ Found entries in response.data.entries')
      } else if (response.data?.data && Array.isArray(response.data.data)) {
        entriesData = response.data.data
        console.log('✅ Found entries in response.data.data')
      } else if (Array.isArray(response.data)) {
        entriesData = response.data
        console.log('✅ Found entries directly in response.data')
      } else {
        console.log('❌ No valid entries array found in response')
        entriesData = []
      }

      console.log('📋 Extracted entries data:', entriesData)
      console.log('📋 Entries count:', entriesData.length)

      if (entriesData.length > 0) {
        console.log('📋 First entry sample:', entriesData[0])
        console.log('📋 Entry keys:', Object.keys(entriesData[0] || {}))
      }

      setEntries(entriesData)
      console.log('✅ Entries set in state:', entriesData.length, 'entries')
      showToast(`Entries loaded successfully (${entriesData.length} entries)`, 'success')
    } catch (error) {
      console.error('❌ Error fetching entries:', error)
      console.error('❌ Error details:', error.response?.data)
      setEntries([])
      showToast('Failed to load entries', 'error')
    } finally {
      setLoading(false)
    }
  }

  const createEntry = async (entryData) => {
    setLoading(true)
    try {
      const response = await axiosInstance.post(API_PATHS.ENTRIES.CREATE_ENTRY, entryData)
      console.log('Created entry:', response.data)
      await fetchAllEntries()
      showToast('Entry created successfully', 'success')
      return response.data
    } catch (error) {
      console.error('Error creating entry:', error)
      showToast('Failed to create entry', 'error')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateEntry = async (entryId, entryData) => {
    setLoading(true)
    try {
      const response = await axiosInstance.put(API_PATHS.ENTRIES.UPDATE_ENTRY_BY_ID(entryId), entryData)
      console.log('Updated entry:', response.data)
      await fetchAllEntries()
      showToast('Entry updated successfully', 'success')
      return response.data
    } catch (error) {
      console.error('Error updating entry:', error)
      showToast('Failed to update entry', 'error')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const deleteEntry = async (entryId) => {
    setLoading(true)
    try {
      await axiosInstance.delete(API_PATHS.ENTRIES.DELETE_ENTRY_BY_ID(entryId))
      console.log('Deleted entry:', entryId)
      await fetchAllEntries()
      showToast('Entry deleted successfully', 'success')
    } catch (error) {
      console.error('Error deleting entry:', error)
      showToast('Failed to delete entry', 'error')
    } finally {
      setLoading(false)
    }
  }

  const getEntryById = async (entryId) => {
    setLoading(true)
    try {
      const response = await axiosInstance.get(API_PATHS.ENTRIES.GET_ENTRY_BY_ID(entryId))
      console.log('Fetched entry by ID:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching entry by ID:', error)
      showToast('Failed to fetch entry details', 'error')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Utility Functions
  const showToast = (message, type = 'success') => {
    setToast({ visible: true, message, type })
    setTimeout(() => setToast(t => ({ ...t, visible: false })), 3000)
  }

  // Load entries on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await fetchAllEntries()
      } catch (error) {
        console.error('Failed to load initial data:', error)
        setEntries([])
      }
    }

    loadInitialData()
  }, [])

  // Debug entries state changes
  useEffect(() => {
    console.log('📊 Entries state changed:', {
      entriesCount: (entries || []).length,
      entries: entries,
      firstEntry: entries?.[0]
    })
  }, [entries])

  const handleAddEntry = async () => {
    if (
      !formData.district ||
      !formData.totalChildren ||
      !formData.outOfSchoolChildren ||
      !formData.date ||
      !formData.poverty ||
      !formData.distance ||
      !formData.other
    ) {
      showToast('Please fill in all required fields', 'error')
      return
    }

    try {
      if (isEditMode && editingEntryId) {
        await updateEntry(editingEntryId, formData)
        setIsEditMode(false)
        setEditingEntryId(null)
      } else {
        await createEntry(formData)
      }

      setShowSuccessMessage(true)
      setTimeout(() => setShowSuccessMessage(false), 3000)

      handleReset()
      setShowForm(false)
    } catch (error) {
      console.error('Error in handleAddEntry:', error)
    }
  }

  const handleEditEntry = async (entryId) => {
    try {
      const entryData = await getEntryById(entryId)
      const entry = entryData.entry || entryData

      setFormData({
        managementSystem: entry.managementSystem || 'District',
        district: entry.district || '',
        totalChildren: entry.totalChildren || '',
        outOfSchoolChildren: entry.outOfSchoolChildren || '',
        gender: entry.gender || { girls: '', boys: '' },
        programType: entry.programType || '',
        date: entry.date || '',
        poverty: entry.poverty || '',
        distance: entry.distance || '',
        other: entry.other || ''
      })

      setIsEditMode(true)
      setEditingEntryId(entryId)
      setShowForm(true)
    } catch (error) {
      console.error('Error in handleEditEntry:', error)
    }
  }

  const handleDeleteEntry = async (entryId) => {
    if (window.confirm('Are you sure you want to delete this entry?')) {
      try {
        await deleteEntry(entryId)
      } catch (error) {
        console.error('Error in handleDeleteEntry:', error)
      }
    }
  }

  const handleReset = () => {
    setFormData({
      managementSystem: 'District',
      district: '',
      totalChildren: '',
      outOfSchoolChildren: '',
      gender: { girls: '', boys: '' },
      programType: '',
      date: '',
      poverty: '',
      distance: '',
      other: ''
    })
    setShowSuccessMessage(false)
    setIsEditMode(false)
    setEditingEntryId(null)
  }

  const handleCancelEdit = () => {
    handleReset()
    setShowForm(false)
  }

  const handleShowForm = () => {
    setShowForm(true)
    setIsEditMode(false)
    setEditingEntryId(null)
  }

  const handleUploadCSV = () => {
    console.log('Uploading CSV')
  }

  // Test API endpoint directly
  const testAPIEndpoint = async () => {
    console.log('🧪 Testing API endpoint directly...')
    try {
      const fullUrl = `${axiosInstance.defaults.baseURL}${API_PATHS.ENTRIES.GET_ALL_ENTRIES}`
      console.log('🌐 Full URL:', fullUrl)

      // Test with fetch
      const fetchResponse = await fetch(fullUrl)
      console.log('🔗 Fetch response status:', fetchResponse.status)
      const fetchData = await fetchResponse.json()
      console.log('🔗 Fetch response data:', fetchData)

      // Test with axios
      const axiosResponse = await axiosInstance.get(API_PATHS.ENTRIES.GET_ALL_ENTRIES)
      console.log('📡 Axios response:', axiosResponse)

    } catch (error) {
      console.error('🧪 Test API error:', error)
    }
  }

  // Debug entries state and filtering
  console.log('🔍 Debug Info:')
  console.log('  - entries state:', entries)
  console.log('  - entries length:', (entries || []).length)
  console.log('  - searchTerm:', searchTerm)

  const filteredEntries = (entries || []).filter(entry => {
    if (!entry) {
      console.log('⚠️ Found null/undefined entry')
      return false
    }

    // If no search term, return all entries
    if (!searchTerm || searchTerm.trim() === '') {
      return true
    }

    const searchLower = searchTerm.toLowerCase()
    const matchesDistrict = entry?.district?.toLowerCase().includes(searchLower)
    const matchesProgram = entry?.programType?.toLowerCase().includes(searchLower)
    const matchesDate = entry?.date?.toLowerCase().includes(searchLower)

    const matches = matchesDistrict || matchesProgram || matchesDate

    if (matches) {
      console.log('✅ Entry matches search:', entry.district, entry.programType, entry.date)
    }

    return matches
  })

  console.log('📊 Filtered entries:', filteredEntries)
  console.log('📊 Filtered entries length:', filteredEntries.length)

  return (
    <div className="min-h-screen bg-[#F8F9FA] py-6 px-2 md:px-6">
      {toast.visible && (
        <Toast message={toast.message} type={toast.type} onClose={() => setToast(t => ({ ...t, visible: false }))} />
      )}

      {/* Header Section */}
      <div className="max-w-7xl mx-auto mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Data Management</h1>
              <p className="text-gray-600 mt-1">Manage your entries and data records</p>
            </div>

            <div className="flex flex-col md:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search entries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full md:w-64"
                />
              </div>

              <button
                onClick={testAPIEndpoint}
                className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-colors duration-200 mr-2"
              >
                🧪 Test API
              </button>

              <button
                onClick={handleShowForm}
                className="bg-[#4A90E2] hover:bg-[#2c5aa0] text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-colors duration-200"
              >
                <Plus className="w-5 h-5" />
                Add Entry
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Form Section */}
      {showForm && (
        <div className="max-w-3xl mx-auto mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 md:p-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">
                {isEditMode ? 'Edit Entry' : 'Add New Entry'}
              </h2>
              <button
                onClick={handleCancelEdit}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form className="w-full">
              {/* District */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">District</label>
                <input
                  type="text"
                  value={formData.district}
                  onChange={(e) => handleInputChange('district', e.target.value)}
                  className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                  placeholder="Mardan"
                />
              </div>

              {/* Row: Total Children & Out-of-School Children */}
              <div className="flex flex-col md:flex-row md:space-x-6 mb-4">
                <div className="flex-1 mb-4 md:mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Children</label>
                  <input
                    type="text"
                    value={formData.totalChildren}
                    onChange={(e) => handleInputChange('totalChildren', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="12,000"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Out-of-School Children</label>
                  <input
                    type="text"
                    value={formData.outOfSchoolChildren}
                    onChange={(e) => handleInputChange('outOfSchoolChildren', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="4,500"
                  />
                </div>
              </div>

              {/* Row: Gender Distribution */}
              <div className="flex flex-col md:flex-row md:space-x-6 mb-4">
                <div className="flex-1 mb-4 md:mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Girls %</label>
                  <input
                    type="text"
                    value={formData.gender.girls}
                    onChange={(e) => handleGenderChange('girls', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="60"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Boys %</label>
                  <input
                    type="text"
                    value={formData.gender.boys}
                    onChange={(e) => handleGenderChange('boys', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="40"
                  />
                </div>
              </div>

              {/* Row: Program Type & Date */}
              <div className="flex flex-col md:flex-row md:space-x-6 mb-4">
                <div className="flex-1 mb-4 md:mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Program Type</label>
                  <select
                    value={formData.programType}
                    onChange={(e) => handleInputChange('programType', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg shadow-sm"
                  >
                    <option value="">Select Program Type</option>
                    <option value="Accelerated Learning Program">Accelerated Learning Program</option>
                    <option value="Non-Formal Education">Non-Formal Education</option>
                    <option value="Community Schools">Community Schools</option>
                    <option value="Mobile Schools">Mobile Schools</option>
                  </select>
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <input
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg shadow-sm"
                  />
                </div>
              </div>

              {/* Row: Barriers */}
              <div className="flex flex-col md:flex-row md:space-x-6 mb-6">
                <div className="flex-1 mb-4 md:mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Poverty %</label>
                  <input
                    type="text"
                    value={formData.poverty}
                    onChange={(e) => handleInputChange('poverty', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="45"
                  />
                </div>
                <div className="flex-1 mb-4 md:mb-0">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Distance %</label>
                  <input
                    type="text"
                    value={formData.distance}
                    onChange={(e) => handleInputChange('distance', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="30"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Other %</label>
                  <input
                    type="text"
                    value={formData.other}
                    onChange={(e) => handleInputChange('other', e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-blue-100 bg-[#F8F9FA] focus:outline-none focus:ring-2 focus:ring-blue-200 text-lg placeholder-gray-400 shadow-sm"
                    placeholder="25"
                  />
                </div>
              </div>

              {/* Success Message */}
              {showSuccessMessage && (
                <div className="mb-4">
                  <span className="text-green-600 text-base">
                    {isEditMode ? 'Entry updated successfully!' : 'Entry added successfully!'}
                  </span>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col md:flex-row md:space-x-4 mt-6">
                <button
                  onClick={handleAddEntry}
                  type="button"
                  disabled={loading}
                  className="w-full md:w-auto mb-2 md:mb-0 bg-[#4A90E2] hover:bg-[#2c5aa0] disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-8 py-2 rounded-lg font-medium text-lg shadow-sm transition-colors duration-200 flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {isEditMode ? 'Updating...' : 'Adding...'}
                    </>
                  ) : (
                    isEditMode ? 'Update Entry' : 'Add Entry'
                  )}
                </button>

                <button
                  onClick={handleReset}
                  type="button"
                  disabled={loading}
                  className="w-full md:w-auto mb-2 md:mb-0 bg-white border border-blue-200 hover:bg-blue-50 disabled:bg-gray-100 disabled:cursor-not-allowed text-[#2c5aa0] px-8 py-2 rounded-lg font-medium text-lg shadow-sm transition-colors duration-200"
                >
                  Reset
                </button>

                <button
                  onClick={handleUploadCSV}
                  type="button"
                  disabled={loading}
                  className="w-full md:w-auto bg-white border border-blue-200 hover:bg-blue-50 disabled:bg-gray-100 disabled:cursor-not-allowed text-[#2c5aa0] px-8 py-2 rounded-lg font-medium text-lg shadow-sm transition-colors duration-200 flex items-center justify-center"
                >
                  <Upload className="w-5 h-5 mr-2" />
                  Upload CSV
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Data Entries Table */}
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 md:p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Data Entries ({filteredEntries.length} of {(entries || []).length})
            </h3>
            {loading && (
              <div className="flex items-center text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-500 mr-2"></div>
                Loading...
              </div>
            )}
          </div>

          {/* Debug table rendering */}
          {console.log('🎨 Rendering table with:', {
            filteredEntriesLength: filteredEntries.length,
            entriesLength: (entries || []).length,
            showingEmptyState: filteredEntries.length === 0
          })}

          {filteredEntries.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">No entries found</div>
              <p className="text-gray-500">
                {(entries || []).length === 0
                  ? "Start by adding your first entry"
                  : "Try adjusting your search criteria"
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">District</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Children</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Out of School</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Girls %</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Boys %</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program Type</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEntries.map((entry) => (
                    <tr key={entry.id || entry._id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.district}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.totalChildren}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.outOfSchoolChildren}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.gender?.girls}%</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.gender?.boys}%</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.programType}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{entry.date}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleEditEntry(entry.id || entry._id)}
                            disabled={loading}
                            className="text-blue-600 hover:text-blue-900 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center"
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteEntry(entry.id || entry._id)}
                            disabled={loading}
                            className="text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center"
                          >
                            <Trash2 className="w-4 h-4 mr-1" />
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default DataManagement
