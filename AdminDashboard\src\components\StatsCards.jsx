import React from 'react'
import { TrendingUp, TrendingDown } from 'lucide-react'

const StatsCards = () => {
  const stats = [
    {
      title: 'Total Children',
      value: '13,13 M',
      bgColor: 'bg-[#4A90E2]',
      textColor: 'text-white',

    },
    {
      title: 'Out Of School',
      value: '4,92 M',
      bgColor: 'bg-[#FFE4CC]',
      textColor: 'text-[#D97706]',

    },
    {
      title: 'Girls',
      value: '48%',
      bgColor: 'bg-[#E1F5FE]',
      textColor: 'text-[#1976D2]',

    },
    {
      title: 'Reduced Demand',
      value: '35',
      bgColor: 'bg-[#F5F5F5]',
      textColor: 'text-[#424242]',

    }
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
      {stats.map((stat, index) => (
        <div
          key={index}
          className={`${stat.bgColor} rounded-lg p-4 md:p-6 shadow-sm`}
        >
          <div className="flex items-center justify-between mb-2 md:mb-3">
            <h3 className={`text-xs md:text-sm font-medium ${stat.textColor} ${stat.bgColor === 'bg-[#4A90E2]' ? 'opacity-90' : 'opacity-80'}`}>
              {stat.title}
            </h3>

          </div>
          <div className={`text-2xl md:text-3xl font-bold ${stat.textColor}`}>
            {stat.value}
          </div>
        </div>
      ))}
    </div>
  )
}

export default StatsCards
