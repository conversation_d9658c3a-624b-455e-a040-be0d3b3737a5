import React, { useState, useEffect } from "react";
import { ChevronDown, Calendar, Users, Target, TrendingUp } from 'lucide-react';
import StatsCards from "../../components/StatsCards";
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar, Tooltip, Legend, PieChart, Pie, Cell } from 'recharts';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import Toast from '../../components/Toast';

export default function Programs() {
    // State management
    const [entries, setEntries] = useState([]);
    const [programs, setPrograms] = useState([]);
    const [selectedProgramType, setSelectedProgramType] = useState('');
    const [loading, setLoading] = useState(false);
    const [toast, setToast] = useState({ visible: false, message: '', type: 'success' });

    const COLORS = ["#4285F4", "#EC4899"];

    // Utility functions
    const showToast = (message, type = 'success') => {
        setToast({ visible: true, message, type });
        setTimeout(() => setToast(t => ({ ...t, visible: false })), 3000);
    };

    // Data processing functions
    const extractUniquePrograms = (entriesData) => {
        const programTypes = [...new Set(entriesData
            .map(entry => entry.programType)
            .filter(type => type && type.trim() !== '')
        )];

        return programTypes.map(type => ({
            name: type,
            type: type
        }));
    };

    const filterEntriesByProgram = (entriesData, programType) => {
        if (!programType) return [];
        return entriesData.filter(entry => entry.programType === programType);
    };

    const calculateDistrictEnrollments = (filteredEntries) => {
        const districtMap = {};

        filteredEntries.forEach(entry => {
            const district = entry.district || 'Unknown';
            const enrolled = parseInt(entry.totalChildren) || 0;

            if (districtMap[district]) {
                districtMap[district] += enrolled;
            } else {
                districtMap[district] = enrolled;
            }
        });

        return Object.entries(districtMap)
            .map(([district, enrolled]) => ({ district, enrolled }))
            .sort((a, b) => b.enrolled - a.enrolled);
    };

    const calculateGenderDistribution = (filteredEntries) => {
        if (filteredEntries.length === 0) return [{ name: "Boys", value: 50 }, { name: "Girls", value: 50 }];

        let totalGirls = 0;
        let totalBoys = 0;
        let validEntries = 0;

        filteredEntries.forEach(entry => {
            const girls = parseFloat(entry.girlsPercentage) || 0;
            const boys = parseFloat(entry.boysPercentage) || 0;

            if (girls > 0 || boys > 0) {
                totalGirls += girls;
                totalBoys += boys;
                validEntries++;
            }
        });

        if (validEntries === 0) return [{ name: "Boys", value: 50 }, { name: "Girls", value: 50 }];

        const avgGirls = Math.round(totalGirls / validEntries);
        const avgBoys = Math.round(totalBoys / validEntries);

        return [
            { name: "Boys", value: avgBoys },
            { name: "Girls", value: avgGirls }
        ];
    };

    const calculateTrendData = (filteredEntries) => {
        if (filteredEntries.length === 0) return [];

        const yearMap = {};

        filteredEntries.forEach(entry => {
            if (entry.date) {
                const year = new Date(entry.date).getFullYear();
                const outOfSchool = parseInt(entry.outOfSchoolChildren) || 0;
                const total = parseInt(entry.totalChildren) || 0;

                if (total > 0) {
                    const percentage = (outOfSchool / total) * 100;

                    if (yearMap[year]) {
                        yearMap[year].push(percentage);
                    } else {
                        yearMap[year] = [percentage];
                    }
                }
            }
        });

        return Object.entries(yearMap)
            .map(([year, percentages]) => ({
                year: year.toString(),
                value: parseFloat((percentages.reduce((sum, p) => sum + p, 0) / percentages.length).toFixed(2))
            }))
            .sort((a, b) => parseInt(a.year) - parseInt(b.year));
    };

    const calculateStatistics = (filteredEntries) => {
        if (filteredEntries.length === 0) return {};

        const totalEnrollments = filteredEntries.reduce((sum, entry) => {
            return sum + (parseInt(entry.totalChildren) || 0);
        }, 0);

        const totalOutOfSchool = filteredEntries.reduce((sum, entry) => {
            return sum + (parseInt(entry.outOfSchoolChildren) || 0);
        }, 0);

        const completionRate = totalEnrollments > 0
            ? Math.round(((totalEnrollments - totalOutOfSchool) / totalEnrollments) * 100)
            : 0;

        const activeDistricts = new Set(filteredEntries.map(entry => entry.district)).size;

        return {
            totalEnrollments,
            completionRate,
            activeDistricts,
            totalOutOfSchool
        };
    };

    // API function to fetch all entries
    const fetchAllEntries = async () => {
        setLoading(true);
        try {
            console.log('🔄 Fetching all entries...');
            const response = await axiosInstance.get(API_PATHS.ENTRIES.GET_ALL_ENTRIES);
            console.log('📋 Entries response:', response.data);

            const entriesData = response.data?.entries || response.data || [];
            const validEntries = Array.isArray(entriesData) ? entriesData : [];

            setEntries(validEntries);

            // Extract unique programs from entries
            const uniquePrograms = extractUniquePrograms(validEntries);
            setPrograms(uniquePrograms);

            console.log('📊 Extracted programs:', uniquePrograms);
            showToast(`Loaded ${validEntries.length} entries with ${uniquePrograms.length} programs`, 'success');
        } catch (error) {
            console.error('❌ Error fetching entries:', error);
            setEntries([]);
            setPrograms([]);
            showToast('Failed to load program data', 'error');
        } finally {
            setLoading(false);
        }
    };

    // Load entries on component mount
    useEffect(() => {
        fetchAllEntries();
    }, []);

    // Handle program selection
    const handleProgramSelect = (programType) => {
        setSelectedProgramType(programType);
        console.log('📌 Selected program type:', programType);
    };

    // Get filtered entries and calculated data
    const getFilteredData = () => {
        const filteredEntries = filterEntriesByProgram(entries, selectedProgramType);

        return {
            filteredEntries,
            enrollmentsData: calculateDistrictEnrollments(filteredEntries),
            genderData: calculateGenderDistribution(filteredEntries),
            trendData: calculateTrendData(filteredEntries),
            statistics: calculateStatistics(filteredEntries)
        };
    };

    const currentData = getFilteredData();

    return (
        <>
            {toast.visible && (
                <Toast
                    message={toast.message}
                    type={toast.type}
                    onClose={() => setToast(t => ({ ...t, visible: false }))}
                />
            )}

            <div className="p-4 md:p-6 bg-[#F8F9FA]">
                {/* Program Selection Dropdown */}
                <div className="max-w-7xl mx-auto mb-6">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 md:p-6">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">Programs Dashboard</h1>
                                <p className="text-gray-600 mt-1">Select a program to view detailed analytics and metrics</p>
                            </div>

                            <div className="relative">
                                <select
                                    value={selectedProgramType}
                                    onChange={(e) => handleProgramSelect(e.target.value)}
                                    className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-64"
                                    disabled={loading}
                                >
                                    <option value="">Select a Program</option>
                                    {programs.map((program) => (
                                        <option key={program.type} value={program.type}>
                                            {program.name}
                                        </option>
                                    ))}
                                </select>
                                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Program Information Card */}
                {selectedProgramType && (
                    <div className="max-w-7xl mx-auto mb-6">
                        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-sm p-4 md:p-6 text-white">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                <div className="flex items-center gap-4">
                                    <div className="bg-white/20 rounded-lg p-3">
                                        <Target className="w-8 h-8" />
                                    </div>
                                    <div>
                                        <h2 className="text-xl font-bold">{selectedProgramType}</h2>
                                        <p className="text-blue-100 mt-1">
                                            {currentData.filteredEntries.length} entries found
                                        </p>
                                    </div>
                                </div>

                                <div className="flex flex-col md:flex-row gap-4 md:gap-8">
                                    <div className="flex items-center gap-2">
                                        <Users className="w-5 h-5 text-blue-200" />
                                        <div>
                                            <p className="text-sm text-blue-200">Total Children</p>
                                            <p className="font-semibold">{currentData.statistics.totalEnrollments?.toLocaleString() || '0'}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <TrendingUp className="w-5 h-5 text-blue-200" />
                                        <div>
                                            <p className="text-sm text-blue-200">Completion Rate</p>
                                            <p className="font-semibold">{currentData.statistics.completionRate || 0}%</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-5 h-5 text-blue-200" />
                                        <div>
                                            <p className="text-sm text-blue-200">Active Districts</p>
                                            <p className="font-semibold">{currentData.statistics.activeDistricts || 0}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <div className="max-w-7xl mx-auto space-y-4 md:space-y-6 pb-4">
                    <StatsCards />
                </div>

                <div className="grid md:grid-cols-2 gap-4 md:gap-6 sm:grid-cols-1">
                    {/* Enrollments Chart */}
                    <div className="bg-white rounded-lg p-4 md:p-6 shadow-sm border border-gray-100">
                        <div className="flex items-center justify-between mb-4 md:mb-6">
                            <h3 className="text-base md:text-lg font-semibold text-gray-900">
                                Enrollments by District {selectedProgramType ? `(${selectedProgramType})` : ''}
                            </h3>
                            {loading && (
                                <div className="flex items-center text-gray-500">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                                    Loading...
                                </div>
                            )}
                        </div>

                        {currentData.enrollmentsData.length > 0 ? (
                            <div className="overflow-x-auto">
                                <ResponsiveContainer width="100%" height={200}>
                                    <BarChart data={currentData.enrollmentsData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                        <XAxis dataKey="district" tick={{ fontSize: 12, fill: '#6b7280' }} />
                                        <YAxis tick={{ fontSize: 12, fill: '#6b7280' }} />
                                        <Tooltip />
                                        <Legend />
                                        <Bar dataKey="enrolled" fill="#4A90E2" />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        ) : (
                            <div className="flex items-center justify-center h-48 text-gray-500">
                                <div className="text-center">
                                    <p className="text-lg mb-2">No enrollment data available</p>
                                    <p className="text-sm">
                                        {selectedProgramType ? 'No entries found for this program type' : 'Please select a program to view data'}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Trend Chart */}
                    <div className="bg-white rounded-lg p-4 md:p-6 shadow-sm border border-gray-100">
                        <div className="flex items-center justify-between mb-4 md:mb-6">
                            <h3 className="text-base md:text-lg font-semibold text-gray-900">
                                OOSC Trend Overview {selectedProgramType ? `(${selectedProgramType})` : ''}
                            </h3>
                            {loading && (
                                <div className="flex items-center text-gray-500">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                                    Loading...
                                </div>
                            )}
                        </div>

                        {currentData.trendData.length > 0 ? (
                            <div className="h-48 md:h-64">
                                <ResponsiveContainer width="100%" height={200}>
                                    <LineChart data={currentData.trendData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                        <XAxis
                                            dataKey="year"
                                            axisLine={false}
                                            tickLine={false}
                                            tick={{ fontSize: 12, fill: '#6b7280' }}
                                        />
                                        <YAxis
                                            axisLine={false}
                                            tickLine={false}
                                            tick={{ fontSize: 12, fill: '#6b7280' }}
                                        />
                                        <Tooltip formatter={(value) => [`${value}%`, 'OOSC Rate']} />
                                        <Line
                                            type="monotone"
                                            dataKey="value"
                                            stroke="#4A90E2"
                                            strokeWidth={2}
                                            dot={{ fill: '#4A90E2', strokeWidth: 2, r: 3 }}
                                            activeDot={{ r: 5, fill: '#2c5aa0' }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        ) : (
                            <div className="flex items-center justify-center h-48 text-gray-500">
                                <div className="text-center">
                                    <p className="text-lg mb-2">No trend data available</p>
                                    <p className="text-sm">
                                        {selectedProgramType ? 'No date information found for this program' : 'Please select a program to view trends'}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Gender Distribution and Actions */}
                <div className="flex flex-col md:flex-row gap-4 md:gap-6 bg-white rounded-lg p-4 md:p-6 shadow-sm border border-gray-100 mt-4">
                    <div className="bg-[#F8FAFC] rounded-lg p-4 shadow border border-blue-100 w-full md:w-1/2 flex flex-col items-center">
                        <div className="flex items-center justify-between w-full mb-4">
                            <h3 className="text-center text-base font-semibold text-gray-900">
                                Gender Distribution {selectedProgramType ? `(${selectedProgramType})` : ''}
                            </h3>
                            {loading && (
                                <div className="flex items-center text-gray-500">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                                </div>
                            )}
                        </div>
                        <div className="w-full flex justify-center">
                            <ResponsiveContainer width="100%" height={200}>
                                <PieChart>
                                    <Pie
                                        data={currentData.genderData}
                                        cx="50%"
                                        cy="50%"
                                        innerRadius={0}
                                        outerRadius={60}
                                        paddingAngle={0}
                                        dataKey="value"
                                    >
                                        {currentData.genderData.map((_, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Legend
                                        iconType="circle"
                                        formatter={(value) => {
                                            const item = currentData.genderData.find(d => d.name === value);
                                            return `${value} – ${item?.value}%`;
                                        }}
                                        layout="horizontal"
                                        verticalAlign="bottom"
                                        align="center"
                                    />
                                </PieChart>
                            </ResponsiveContainer>
                        </div>
                    </div>

                    <div className="flex flex-col items-center justify-center w-full md:w-1/2 space-y-2 pt-4">
                        {selectedProgramType && (
                            <div className="w-full mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <h4 className="font-semibold text-blue-900 mb-2">Program Statistics</h4>
                                <div className="grid grid-cols-1 gap-2 text-sm">
                                    <div>
                                        <span className="text-blue-600">Total Enrollments:</span>
                                        <span className="font-semibold ml-1">{currentData.statistics.totalEnrollments?.toLocaleString() || '0'}</span>
                                    </div>
                                    <div>
                                        <span className="text-blue-600">Out of School:</span>
                                        <span className="font-semibold ml-1">{currentData.statistics.totalOutOfSchool?.toLocaleString() || '0'}</span>
                                    </div>
                                    <div>
                                        <span className="text-blue-600">Completion Rate:</span>
                                        <span className="font-semibold ml-1">{currentData.statistics.completionRate || 0}%</span>
                                    </div>
                                    <div>
                                        <span className="text-blue-600">Active Districts:</span>
                                        <span className="font-semibold ml-1">{currentData.statistics.activeDistricts || 0}</span>
                                    </div>
                                    <div>
                                        <span className="text-blue-600">Data Entries:</span>
                                        <span className="font-semibold ml-1">{currentData.filteredEntries.length}</span>
                                    </div>
                                </div>
                            </div>
                        )}

                        <button
                            className="text-black text-sm font-medium bg-transparent border border-gray-500 px-4 py-2 rounded-lg w-full transition-colors duration-200 hover:bg-gray-50"
                            disabled={loading}
                        >
                            Edit Program Info
                        </button>
                        <button
                            className="text-white text-sm font-medium bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-lg w-full transition-colors duration-200 disabled:bg-blue-300"
                            disabled={loading || !selectedProgramType}
                        >
                            Export Program Data
                        </button>
                        <button
                            className="text-white text-sm font-medium bg-green-500 hover:bg-green-600 px-4 py-2 rounded-lg w-full transition-colors duration-200 disabled:bg-green-300"
                            disabled={loading || !selectedProgramType}
                        >
                            Add Beneficiary
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
}
