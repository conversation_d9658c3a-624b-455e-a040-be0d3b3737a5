import React, { useState, useEffect } from "react";
import { ChevronDown, Calendar, Users, Target, TrendingUp } from 'lucide-react';
import StatsCards from "../../components/StatsCards";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar, Tooltip, Legend, PieChart, Pie, Cell } from 'recharts';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import Toast from '../../components/Toast';

export default function Programs() {
    // State management
    const [programs, setPrograms] = useState([]);
    const [selectedProgram, setSelectedProgram] = useState(null);
    const [programData, setProgramData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [toast, setToast] = useState({ visible: false, message: '', type: 'success' });

    // Default/fallback data
    const defaultTrendData = [
        { year: '2019', value: 4.2 },
        { year: '2020', value: 4.5 },
        { year: '2021', value: 4.8 },
        { year: '2022', value: 4.6 },
        { year: '2023', value: 4.9 },
        { year: '2024', value: 4.92 }
    ];

    const defaultEnrollmentsData = [
        { district: 'Peshawar', enrolled: 120 },
        { district: 'Swat', enrolled: 98 },
        { district: 'Mardan', enrolled: 85 },
        { district: 'Bannu', enrolled: 65 },
        { district: 'Other', enrolled: 40 }
    ];

    const defaultGenderData = [
        { name: "Boys", value: 54 },
        { name: "Girls", value: 46 },
    ];

    const COLORS = ["#4285F4", "#EC4899"];

    // Utility functions
    const showToast = (message, type = 'success') => {
        setToast({ visible: true, message, type });
        setTimeout(() => setToast(t => ({ ...t, visible: false })), 3000);
    };

    // API functions
    const fetchAllPrograms = async () => {
        try {
            console.log('🔄 Fetching all programs...');
            const response = await axiosInstance.get(API_PATHS.PROGRAMS.GET_ALL_PROGRAMS);
            console.log('📋 Programs response:', response.data);

            const programsData = response.data?.programs || response.data || [];
            setPrograms(Array.isArray(programsData) ? programsData : []);
            showToast(`Loaded ${programsData.length} programs`, 'success');
        } catch (error) {
            console.error('❌ Error fetching programs:', error);
            // Use fallback data if API fails
            const fallbackPrograms = [
                { id: 1, name: 'Voucher Program', startDate: '2024-01-01', status: 'Active' },
                { id: 2, name: 'Accelerated Learning Program', startDate: '2024-02-01', status: 'Active' },
                { id: 3, name: 'Community Schools Program', startDate: '2024-03-01', status: 'Active' }
            ];
            setPrograms(fallbackPrograms);
            showToast('Using sample program data', 'info');
        }
    };

    const fetchProgramData = async (programId) => {
        if (!programId) return;

        setLoading(true);
        try {
            console.log('🔄 Fetching data for program:', programId);
            const response = await axiosInstance.get(API_PATHS.PROGRAMS.GET_PROGRAM_DATA(programId));
            console.log('📊 Program data response:', response.data);

            setProgramData(response.data);
            showToast('Program data loaded successfully', 'success');
        } catch (error) {
            console.error('❌ Error fetching program data:', error);
            // Use fallback data if API fails
            const fallbackData = {
                trendData: defaultTrendData,
                enrollmentsData: defaultEnrollmentsData,
                genderData: defaultGenderData,
                statistics: {
                    totalEnrollments: 408,
                    completionRate: 78,
                    activeDistricts: 5,
                    averageAge: 12
                }
            };
            setProgramData(fallbackData);
            showToast('Using sample program data', 'info');
        } finally {
            setLoading(false);
        }
    };

    // Load programs on component mount
    useEffect(() => {
        fetchAllPrograms();
    }, []);

    // Handle program selection
    const handleProgramSelect = (programId) => {
        const program = programs.find(p => p.id === parseInt(programId));
        setSelectedProgram(program);
        if (program) {
            fetchProgramData(program.id);
        } else {
            setProgramData(null);
        }
    };

    // Get current data (either from selected program or defaults)
    const getCurrentData = () => {
        if (programData) {
            return {
                trendData: programData.trendData || defaultTrendData,
                enrollmentsData: programData.enrollmentsData || defaultEnrollmentsData,
                genderData: programData.genderData || defaultGenderData,
                statistics: programData.statistics || {}
            };
        }
        return {
            trendData: defaultTrendData,
            enrollmentsData: defaultEnrollmentsData,
            genderData: defaultGenderData,
            statistics: {}
        };
    };

    const currentData = getCurrentData();

    return (
        <>
            {toast.visible && (
                <Toast
                    message={toast.message}
                    type={toast.type}
                    onClose={() => setToast(t => ({ ...t, visible: false }))}
                />
            )}

            <div className="p-4 md:p-6 bg-[#F8F9FA]">
                {/* Program Selection Dropdown */}
                <div className="max-w-7xl mx-auto mb-6">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 md:p-6">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">Programs Dashboard</h1>
                                <p className="text-gray-600 mt-1">Select a program to view detailed analytics and metrics</p>
                            </div>

                            <div className="relative">
                                <select
                                    value={selectedProgram?.id || ''}
                                    onChange={(e) => handleProgramSelect(e.target.value)}
                                    className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-64"
                                    disabled={loading}
                                >
                                    <option value="">Select a Program</option>
                                    {programs.map((program) => (
                                        <option key={program.id} value={program.id}>
                                            {program.name}
                                        </option>
                                    ))}
                                </select>
                                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Program Information Card */}
                {selectedProgram && (
                    <div className="max-w-7xl mx-auto mb-6">
                        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-sm p-4 md:p-6 text-white">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                <div className="flex items-center gap-4">
                                    <div className="bg-white/20 rounded-lg p-3">
                                        <Target className="w-8 h-8" />
                                    </div>
                                    <div>
                                        <h2 className="text-xl font-bold">{selectedProgram.name}</h2>
                                        <p className="text-blue-100 mt-1">Active Program</p>
                                    </div>
                                </div>

                                <div className="flex flex-col md:flex-row gap-4 md:gap-8">
                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-5 h-5 text-blue-200" />
                                        <div>
                                            <p className="text-sm text-blue-200">Start Date</p>
                                            <p className="font-semibold">{selectedProgram.startDate || 'N/A'}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Users className="w-5 h-5 text-blue-200" />
                                        <div>
                                            <p className="text-sm text-blue-200">Status</p>
                                            <p className="font-semibold">{selectedProgram.status || 'Active'}</p>
                                        </div>
                                    </div>
                                    {currentData.statistics.totalEnrollments && (
                                        <div className="flex items-center gap-2">
                                            <TrendingUp className="w-5 h-5 text-blue-200" />
                                            <div>
                                                <p className="text-sm text-blue-200">Total Enrollments</p>
                                                <p className="font-semibold">{currentData.statistics.totalEnrollments}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <div className="max-w-7xl mx-auto space-y-4 md:space-y-6 pb-4">
                    <StatsCards />
                </div>

                <div className="grid md:grid-cols-2 gap-4 md:gap-6 sm:grid-cols-1">
                    {/* Enrollments Chart */}
                    <div className="bg-white rounded-lg p-4 md:p-6 shadow-sm border border-gray-100">
                        <div className="flex items-center justify-between mb-4 md:mb-6">
                            <h3 className="text-base md:text-lg font-semibold text-gray-900">
                                Enrollments by District {selectedProgram ? `(${selectedProgram.name})` : ''}
                            </h3>
                            {loading && (
                                <div className="flex items-center text-gray-500">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                                    Loading...
                                </div>
                            )}
                        </div>

                        <div className="overflow-x-auto">
                            <ResponsiveContainer width="100%" height={200}>
                                <BarChart data={currentData.enrollmentsData}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                    <XAxis dataKey="district" tick={{ fontSize: 12, fill: '#6b7280' }} />
                                    <YAxis tick={{ fontSize: 12, fill: '#6b7280' }} />
                                    <Tooltip />
                                    <Legend />
                                    <Bar dataKey="enrolled" fill="#4A90E2" />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>

                    {/* Trend Chart */}
                    <div className="bg-white rounded-lg p-4 md:p-6 shadow-sm border border-gray-100">
                        <div className="flex items-center justify-between mb-4 md:mb-6">
                            <h3 className="text-base md:text-lg font-semibold text-gray-900">
                                OOSC Trend Overview {selectedProgram ? `(${selectedProgram.name})` : ''}
                            </h3>
                            {loading && (
                                <div className="flex items-center text-gray-500">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                                    Loading...
                                </div>
                            )}
                        </div>
                        <div className="h-48 md:h-64">
                            <ResponsiveContainer width="100%" height={200}>
                                <LineChart data={currentData.trendData}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                    <XAxis
                                        dataKey="year"
                                        axisLine={false}
                                        tickLine={false}
                                        tick={{ fontSize: 12, fill: '#6b7280' }}
                                    />
                                    <YAxis
                                        axisLine={false}
                                        tickLine={false}
                                        tick={{ fontSize: 12, fill: '#6b7280' }}
                                        domain={[4, 5]}
                                    />
                                    <Line
                                        type="monotone"
                                        dataKey="value"
                                        stroke="#4A90E2"
                                        strokeWidth={2}
                                        dot={{ fill: '#4A90E2', strokeWidth: 2, r: 3 }}
                                        activeDot={{ r: 5, fill: '#2c5aa0' }}
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </div>
                    </div>

                </div>


                {/* Gender Distribution and Actions */}
                <div className="flex flex-col md:flex-row gap-4 md:gap-6 bg-white rounded-lg p-4 md:p-6 shadow-sm border border-gray-100 mt-4">
                    <div className="bg-[#F8FAFC] rounded-lg p-4 shadow border border-blue-100 w-full md:w-1/2 flex flex-col items-center">
                        <div className="flex items-center justify-between w-full mb-4">
                            <h3 className="text-center text-base font-semibold text-gray-900">
                                Gender Distribution {selectedProgram ? `(${selectedProgram.name})` : ''}
                            </h3>
                            {loading && (
                                <div className="flex items-center text-gray-500">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
                                </div>
                            )}
                        </div>
                        <div className="w-full flex justify-center">
                            <ResponsiveContainer width="100%" height={200}>
                                <PieChart>
                                    <Pie
                                        data={currentData.genderData}
                                        cx="50%"
                                        cy="50%"
                                        innerRadius={0}
                                        outerRadius={60}
                                        paddingAngle={0}
                                        dataKey="value"
                                    >
                                        {currentData.genderData.map((_, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Legend
                                        iconType="circle"
                                        formatter={(value) => {
                                            const item = currentData.genderData.find(d => d.name === value);
                                            return `${value} – ${item?.value}%`;
                                        }}
                                        layout="horizontal"
                                        verticalAlign="bottom"
                                        align="center"
                                    />
                                </PieChart>
                            </ResponsiveContainer>
                        </div>
                    </div>

                    <div className="flex flex-col items-center justify-center w-full md:w-1/2 space-y-2 pt-4">
                        {selectedProgram && (
                            <div className="w-full mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <h4 className="font-semibold text-blue-900 mb-2">Program Statistics</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm">
                                    {currentData.statistics.completionRate && (
                                        <div>
                                            <span className="text-blue-600">Completion Rate:</span>
                                            <span className="font-semibold ml-1">{currentData.statistics.completionRate}%</span>
                                        </div>
                                    )}
                                    {currentData.statistics.activeDistricts && (
                                        <div>
                                            <span className="text-blue-600">Active Districts:</span>
                                            <span className="font-semibold ml-1">{currentData.statistics.activeDistricts}</span>
                                        </div>
                                    )}
                                    {currentData.statistics.averageAge && (
                                        <div>
                                            <span className="text-blue-600">Average Age:</span>
                                            <span className="font-semibold ml-1">{currentData.statistics.averageAge} years</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        <button
                            className="text-black text-sm font-medium bg-transparent border border-gray-500 px-4 py-2 rounded-lg w-full transition-colors duration-200 hover:bg-gray-50"
                            disabled={loading}
                        >
                            Edit Program Info
                        </button>
                        <button
                            className="text-white text-sm font-medium bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-lg w-full transition-colors duration-200 disabled:bg-blue-300"
                            disabled={loading || !selectedProgram}
                        >
                            Export Program Data
                        </button>
                        <button
                            className="text-white text-sm font-medium bg-green-500 hover:bg-green-600 px-4 py-2 rounded-lg w-full transition-colors duration-200 disabled:bg-green-300"
                            disabled={loading || !selectedProgram}
                        >
                            Add Beneficiary
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
}
